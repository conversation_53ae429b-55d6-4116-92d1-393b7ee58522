'use client';

import React, { useState, useEffect, useRef } from 'react';
import DashboardCard from './DashboardCard';

interface LazyDashboardCardProps {
  title: string;
  items: any[] | null;
  loading: boolean;
  emptyMessage: string;
  viewAllHref: string;
  createHref: string;
  renderItem: (item: any) => React.ReactNode;
  emptyIcon: React.ReactNode;
}

const LazyDashboardCard: React.FC<LazyDashboardCardProps> = (props) => {
  const [isVisible, setIsVisible] = useState(false);
  const [hasLoaded, setHasLoaded] = useState(false);
  const cardRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting && !hasLoaded) {
          setIsVisible(true);
          setHasLoaded(true);
        }
      },
      {
        threshold: 0.1,
        rootMargin: '50px',
      }
    );

    if (cardRef.current) {
      observer.observe(cardRef.current);
    }

    return () => {
      if (cardRef.current) {
        observer.unobserve(cardRef.current);
      }
    };
  }, [hasLoaded]);

  return (
    <div ref={cardRef} className="min-h-[400px]">
      {isVisible ? (
        <DashboardCard {...props} />
      ) : (
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 animate-pulse">
          <div className="flex items-center justify-between mb-4">
            <div className="h-6 bg-gray-200 rounded w-32"></div>
            <div className="h-4 bg-gray-200 rounded w-16"></div>
          </div>
          <div className="space-y-3">
            {[...Array(3)].map((_, i) => (
              <div key={i} className="flex items-center space-x-3">
                <div className="h-12 w-12 bg-gray-200 rounded"></div>
                <div className="flex-1">
                  <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                  <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default LazyDashboardCard;
