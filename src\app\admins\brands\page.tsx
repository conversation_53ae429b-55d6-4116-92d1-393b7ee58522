'use client';

import React, { useState } from 'react';
import AdminLayout from '@/components/AdminLayout';
import { ProtectedRoute } from '@/contexts/AuthContext';
import { useListData, useFormSubmit, useModal, useForm } from '@/lib/hooks';
import { brandAPI } from '@/lib/api';
import { formatDate, truncateText, getInputClassName } from '@/lib/utils';
import ImageUpload from '@/components/ImageUpload';

interface Brand {
  id: number;
  name: string;
  description?: string;
  brand_photo?: string;
  created_at: string;
  updated_at: string;
}

interface BrandForm {
  name: string;
  description: string;
  brand_photo: string;
}

function BrandModal({ isOpen, onClose, brand, onSuccess }: {
  isOpen: boolean;
  onClose: () => void;
  brand?: Brand;
  onSuccess: () => void;
}) {
  const isEdit = !!brand;
  const { loading, error, success, submit, reset } = useFormSubmit();
  const [uploadError, setUploadError] = useState<string | null>(null);
  
  const {
    values,
    errors,
    handleChange,
    setError,
    clearErrors,
    reset: resetForm,
    setValue
  } = useForm<BrandForm>({
    name: brand?.name || '',
    description: brand?.description || '',
    brand_photo: brand?.brand_photo || ''
  });

  const validateForm = (): boolean => {
    clearErrors();
    let isValid = true;

    if (!values.name.trim()) {
      setError('name', 'Brand name is required');
      isValid = false;
    }

    return isValid;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) return;

    const success = await submit(async () => {
      if (isEdit) {
        return brandAPI.updateBrand(brand.id, values);
      } else {
        return brandAPI.createBrand(values);
      }
    });

    if (success) {
      onSuccess();
      onClose();
      resetForm();
      reset();
    }
  };

  const handleClose = () => {
    onClose();
    resetForm();
    reset();
    setUploadError(null);
  };

  const handleImageUploaded = (imageUrl: string) => {
    setValue('brand_photo', imageUrl);
    setUploadError(null);
  };

  const handleImageError = (error: string) => {
    setUploadError(error);
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
      <div className="relative top-4 mx-auto p-6 border w-full max-w-5xl shadow-lg rounded-md bg-white min-h-[90vh]">
        <div className="mb-6">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-2xl font-bold text-gray-900">
              {isEdit ? 'Edit Brand' : 'Create New Brand'}
            </h3>
            <button
              onClick={handleClose}
              className="text-gray-400 hover:text-gray-600 p-2 rounded-full hover:bg-gray-100"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>

          {/* Global Messages */}
          {error && (
            <div className="mb-6 bg-red-50 border border-red-200 rounded-md p-4">
              <div className="flex">
                <svg className="h-5 w-5 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <div className="ml-3">
                  <p className="text-sm text-red-800">{error}</p>
                </div>
              </div>
            </div>
          )}

          {success && (
            <div className="mb-6 bg-green-50 border border-green-200 rounded-md p-4">
              <div className="flex">
                <svg className="h-5 w-5 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <div className="ml-3">
                  <p className="text-sm text-green-800">{success}</p>
                </div>
              </div>
            </div>
          )}

          <form onSubmit={handleSubmit}>
            {/* Two Column Layout */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">

              {/* Column 1: Image Preview & Upload */}
              <div>
                <div className="bg-gray-50 rounded-lg p-6">
                  <h4 className="text-lg font-semibold text-gray-900 mb-4">Brand Image</h4>
                  {uploadError && (
                    <div className="mb-4 bg-red-50 border border-red-200 rounded-md p-3">
                      <p className="text-sm text-red-800">{uploadError}</p>
                    </div>
                  )}

                  {/* Image Preview */}
                  {values.brand_photo && (
                    <div className="mb-4">
                      <div className="relative">
                        <img
                          src={values.brand_photo}
                          alt="Brand preview"
                          className="w-full h-48 object-cover rounded-lg border border-gray-300"
                        />
                        <button
                          type="button"
                          onClick={() => setValue('brand_photo', '')}
                          className="absolute top-2 right-2 bg-red-500 text-white rounded-full p-1 hover:bg-red-600 transition-colors"
                        >
                          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                          </svg>
                        </button>
                      </div>
                    </div>
                  )}

                  <ImageUpload
                    onImageUploaded={handleImageUploaded}
                    onError={handleImageError}
                    uploadType="brand"
                  />
                </div>
              </div>

              {/* Column 2: Brand Form */}
              <div>
                <div className="bg-white border border-gray-200 rounded-lg p-6">
                  <h4 className="text-lg font-semibold text-gray-900 mb-6">Brand Details</h4>
                  <div className="space-y-6">

                    {/* Brand Name */}
                    <div>
                      <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-2">
                        Brand Name *
                      </label>
                      <input
                        type="text"
                        id="name"
                        name="name"
                        value={values.name}
                        onChange={handleChange}
                        className={getInputClassName(!!errors.name)}
                        placeholder="Enter brand name"
                      />
                      {errors.name && (
                        <p className="mt-1 text-sm text-red-600">{errors.name}</p>
                      )}
                    </div>

                    {/* Description */}
                    <div>
                      <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-2">
                        Description
                      </label>
                      <textarea
                        id="description"
                        name="description"
                        rows={4}
                        value={values.description}
                        onChange={handleChange}
                        className={getInputClassName()}
                        placeholder="Enter brand description (optional)"
                      />
                    </div>

                  </div>
                </div>
              </div>

            </div>

            {/* Form Actions */}
            <div className="flex justify-end space-x-4 pt-8 border-t border-gray-200 mt-8">
              <button
                type="button"
                onClick={handleClose}
                className="px-6 py-3 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#E6B120] transition-colors"
              >
                Cancel
              </button>
              <button
                type="submit"
                disabled={loading}
                className={`px-6 py-3 border border-transparent rounded-md shadow-sm text-sm font-medium text-white transition-colors ${
                  loading
                    ? 'bg-gray-400 cursor-not-allowed'
                    : 'bg-[#E6B120] hover:bg-[#FFCD29] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#E6B120]'
                }`}
              >
                {loading ? 'Saving...' : (isEdit ? 'Update Brand' : 'Create Brand')}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}

function BrandsContent() {
  const { items: brands, loading, error, refetch, addItem, updateItem, removeItem } = useListData<Brand>(
    () => brandAPI.getAllBrands()
  );
  const { isOpen, data: selectedBrand, open, close } = useModal();
  const [deleteLoading, setDeleteLoading] = useState<number | null>(null);

  const handleCreate = () => {
    open();
  };

  const handleEdit = (brand: Brand) => {
    open(brand);
  };

  const handleDelete = async (brand: Brand) => {
    if (!confirm(`Are you sure you want to delete "${brand.name}"?`)) return;

    setDeleteLoading(brand.id);
    try {
      const response = await brandAPI.deleteBrand(brand.id);
      if (response.success) {
        removeItem(brand.id);
      } else {
        alert(response.error || 'Failed to delete brand');
      }
    } catch (error: any) {
      alert(error.response?.data?.error || 'Failed to delete brand');
    } finally {
      setDeleteLoading(null);
    }
  };

  const handleModalSuccess = () => {
    refetch();
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#E6B120]"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Brands</h1>
          <p className="text-gray-600">Manage product brands</p>
        </div>
        <button
          onClick={handleCreate}
          className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-[#E6B120] hover:bg-[#FFCD29] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#E6B120]"
        >
          <svg className="-ml-1 mr-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
          </svg>
          Add Brand
        </button>
      </div>

      {/* Error State */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-md p-4">
          <div className="flex">
            <svg className="h-5 w-5 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <div className="ml-3">
              <p className="text-sm text-red-800">{error}</p>
            </div>
          </div>
        </div>
      )}

      {/* Brands List */}
      <div className="bg-white shadow-sm rounded-lg border border-gray-200">
        {brands.length === 0 ? (
          <div className="p-6 text-center">
            <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z" />
            </svg>
            <h3 className="mt-2 text-sm font-medium text-gray-900">No brands</h3>
            <p className="mt-1 text-sm text-gray-500">Get started by creating a new brand.</p>
            <div className="mt-6">
              <button
                onClick={handleCreate}
                className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-[#E6B120] hover:bg-[#FFCD29] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#E6B120]"
              >
                <svg className="-ml-1 mr-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                </svg>
                Add Brand
              </button>
            </div>
          </div>
        ) : (
          <div className="divide-y divide-gray-200">
            {brands.map((brand) => (
              <div key={brand.id} className="p-6 flex items-center justify-between">
                <div className="flex items-center space-x-4 flex-1">
                  {brand.brand_photo && (
                    <img
                      src={brand.brand_photo}
                      alt={brand.name}
                      className="w-16 h-16 object-cover rounded-lg border border-gray-200"
                    />
                  )}
                  <div className="flex-1">
                    <h3 className="text-lg font-medium text-gray-900">{brand.name}</h3>
                  {brand.description && (
                    <p className="text-sm text-gray-500 mt-1">
                      {truncateText(brand.description, 100)}
                    </p>
                  )}
                    <p className="text-xs text-gray-400 mt-2">
                      Created: {formatDate(brand.created_at)}
                    </p>
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  <button
                    onClick={() => handleEdit(brand)}
                    className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#E6B120]"
                  >
                    Edit
                  </button>
                  <button
                    onClick={() => handleDelete(brand)}
                    disabled={deleteLoading === brand.id}
                    className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:opacity-50"
                  >
                    {deleteLoading === brand.id ? 'Deleting...' : 'Delete'}
                  </button>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Modal */}
      <BrandModal
        isOpen={isOpen}
        onClose={close}
        brand={selectedBrand}
        onSuccess={handleModalSuccess}
      />
    </div>
  );
}

export default function BrandsPage() {
  return (
    <ProtectedRoute>
      <AdminLayout>
        <BrandsContent />
      </AdminLayout>
    </ProtectedRoute>
  );
}
