'use client';

import React, { useState, useRef, useCallback } from 'react';
import ReactCrop, { Crop, PixelCrop } from 'react-image-crop';
import 'react-image-crop/dist/ReactCrop.css';

interface BannerImageUploadProps {
  onImageUploaded: (imageUrl: string, filename?: string) => void;
  onError?: (error: string) => void;
  className?: string;
}

interface CropModalProps {
  src: string;
  onCropComplete: (croppedImageBlob: Blob) => void;
  onCancel: () => void;
}

function CropModal({ src, onCropComplete, onCancel }: CropModalProps) {
  const [crop, setCrop] = useState<Crop>({
    unit: '%',
    width: 90,
    height: 34.6, // 90 * (5/13) for 13:5 aspect ratio
    x: 5,
    y: 32.7, // Center vertically
  });
  const [completedCrop, setCompletedCrop] = useState<PixelCrop>();
  const imgRef = useRef<HTMLImageElement>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);

  const onImageLoad = useCallback((e: React.SyntheticEvent<HTMLImageElement>) => {
    const { width, height } = e.currentTarget;
    
    // Calculate crop for 13:5 aspect ratio
    const aspectRatio = 13 / 5;
    let cropWidth = width;
    let cropHeight = width / aspectRatio;
    
    // If calculated height is larger than image height, adjust based on height
    if (cropHeight > height) {
      cropHeight = height;
      cropWidth = height * aspectRatio;
    }
    
    const x = (width - cropWidth) / 2;
    const y = (height - cropHeight) / 2;
    
    setCrop({
      unit: 'px',
      width: cropWidth,
      height: cropHeight,
      x,
      y,
    });
  }, []);

  const getCroppedImg = useCallback(async (): Promise<Blob | null> => {
    if (!completedCrop || !imgRef.current || !canvasRef.current) {
      console.error('Missing required elements for cropping');
      return null;
    }

    const image = imgRef.current;
    const canvas = canvasRef.current;
    const ctx = canvas.getContext('2d');

    if (!ctx) {
      console.error('Could not get canvas context');
      return null;
    }

    const scaleX = image.naturalWidth / image.width;
    const scaleY = image.naturalHeight / image.height;

    canvas.width = completedCrop.width;
    canvas.height = completedCrop.height;

    ctx.drawImage(
      image,
      completedCrop.x * scaleX,
      completedCrop.y * scaleY,
      completedCrop.width * scaleX,
      completedCrop.height * scaleY,
      0,
      0,
      completedCrop.width,
      completedCrop.height
    );

    return new Promise<Blob | null>((resolve, reject) => {
      canvas.toBlob((blob) => {
        if (blob) {
          resolve(blob);
        } else {
          reject(new Error('Failed to create blob from canvas'));
        }
      }, 'image/jpeg', 0.9);
    });
  }, [completedCrop]);

  const handleCropComplete = async () => {
    try {
      // If completedCrop is not set, use the current crop
      if (!completedCrop && crop && imgRef.current) {
        const image = imgRef.current;
        const scaleX = image.naturalWidth / image.width;
        const scaleY = image.naturalHeight / image.height;

        setCompletedCrop({
          x: crop.x * scaleX,
          y: crop.y * scaleY,
          width: crop.width * scaleX,
          height: crop.height * scaleY,
          unit: 'px'
        });

        // Wait a bit for the state to update
        setTimeout(async () => {
          try {
            const croppedImageBlob = await getCroppedImg();
            if (croppedImageBlob) {
              onCropComplete(croppedImageBlob);
            }
          } catch (error) {
            console.error('Error during delayed crop completion:', error);
          }
        }, 100);
        return;
      }

      const croppedImageBlob = await getCroppedImg();
      if (croppedImageBlob) {
        onCropComplete(croppedImageBlob);
      } else {
        console.error('Failed to get cropped image blob');
      }
    } catch (error) {
      console.error('Error during crop completion:', error);
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 max-w-6xl max-h-[90vh] overflow-auto">
        <h3 className="text-lg font-medium text-gray-900 mb-4">
          Crop Banner Image (13:5 Aspect Ratio)
        </h3>
        
        <div className="mb-4">
          <ReactCrop
            crop={crop}
            onChange={(_, percentCrop) => setCrop(percentCrop)}
            onComplete={(c) => setCompletedCrop(c)}
            aspect={13/5}
            minWidth={260}
            minHeight={100}
          >
            <img
              ref={imgRef}
              alt="Crop preview"
              src={src}
              style={{ maxHeight: '60vh', maxWidth: '100%' }}
              onLoad={onImageLoad}
            />
          </ReactCrop>
        </div>

        <canvas
          ref={canvasRef}
          style={{ display: 'none' }}
        />

        <div className="flex justify-end space-x-3">
          <button
            onClick={onCancel}
            className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50"
          >
            Cancel
          </button>
          <button
            onClick={handleCropComplete}
            className="px-4 py-2 bg-[#E6B120] text-white rounded-md text-sm font-medium hover:bg-[#FFCD29]"
          >
            Crop & Upload
          </button>
        </div>
      </div>
    </div>
  );
}

export default function BannerImageUpload({ 
  onImageUploaded, 
  onError, 
  className = ''
}: BannerImageUploadProps) {
  const [isUploading, setIsUploading] = useState(false);
  const [cropModalSrc, setCropModalSrc] = useState<string | null>(null);
  const [pendingFile, setPendingFile] = useState<File | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (!files || files.length === 0) return;

    const file = files[0];
    
    // Validate file type
    if (!file.type.startsWith('image/')) {
      onError?.('Please select an image file.');
      return;
    }

    // Validate file size (10MB)
    if (file.size > 10 * 1024 * 1024) {
      onError?.('File size must be less than 10MB.');
      return;
    }

    // Create preview URL for cropping
    const reader = new FileReader();
    reader.onload = (e) => {
      if (e.target?.result) {
        setCropModalSrc(e.target.result as string);
        setPendingFile(file);
      }
    };
    reader.readAsDataURL(file);
  };

  const handleCropComplete = async (croppedBlob: Blob) => {
    if (!pendingFile) return;

    setIsUploading(true);
    setCropModalSrc(null);

    try {
      const formData = new FormData();
      formData.append('image', croppedBlob, pendingFile.name);

      const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5000/api';
      const response = await fetch(`${API_BASE_URL}/upload/banner-image`, {
        method: 'POST',
        body: formData,
        credentials: 'include',
      });

      const result = await response.json();

      if (result.success) {
        onImageUploaded(result.data.url, result.data.filename);
      } else {
        onError?.(result.error || 'Upload failed');
      }
    } catch (error) {
      onError?.('Network error during upload');
    } finally {
      setIsUploading(false);
      setPendingFile(null);
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    }
  };

  const handleCropCancel = () => {
    setCropModalSrc(null);
    setPendingFile(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  return (
    <>
      <div className={className}>
        <input
          ref={fileInputRef}
          type="file"
          accept="image/*"
          onChange={handleFileSelect}
          className="hidden"
        />
        
        <button
          type="button"
          onClick={() => fileInputRef.current?.click()}
          disabled={isUploading}
          className="w-full border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-gray-400 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#E6B120] disabled:opacity-50"
        >
          {isUploading ? (
            <div className="flex items-center justify-center">
              <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-[#E6B120]"></div>
              <span className="ml-2 text-sm text-gray-600">Uploading...</span>
            </div>
          ) : (
            <div className="space-y-2">
              <svg className="mx-auto h-12 w-12 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
              </svg>
              <span className="text-sm text-gray-600">
                Click to upload banner image
              </span>
              <span className="text-xs text-gray-400 mt-1">
                Will be cropped to 13:5 ratio and converted to WebP
              </span>
            </div>
          )}
        </button>
      </div>

      {cropModalSrc && (
        <CropModal
          src={cropModalSrc}
          onCropComplete={handleCropComplete}
          onCancel={handleCropCancel}
        />
      )}
    </>
  );
}
