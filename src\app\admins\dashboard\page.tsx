'use client';

import React, { useEffect, useState, useMemo, useCallback } from 'react';
import Link from 'next/link';
import AdminLayout from '@/components/AdminLayout';
import { ProtectedRoute } from '@/contexts/AuthContext';
import { useFetch } from '@/lib/hooks';
import { productAPI, brandAPI, categoryAPI, bannerAPI } from '@/lib/api';
import { useProducts, useBrands, useCategories, useBanners } from '@/lib/queries';
import { formatDate } from '@/lib/utils';
import DashboardCard, {
  ProductItemRenderer,
  BrandItemRenderer,
  CategoryItemRenderer,
  BannerItemRenderer
} from '@/components/DashboardCard';
import LazyDashboardCard from '@/components/LazyDashboardCard';

interface DashboardStats {
  totalProducts: number;
  totalBrands: number;
  totalCategories: number;
  totalBanners: number;
}

interface StatCardProps {
  title: string;
  value: number;
  icon: React.ReactNode;
  color: string;
  href: string;
}

const StatCard = React.memo(function StatCard({ title, value, icon, color, href }: StatCardProps) {
  return (
    <Link href={href} className="block">
      <div className="bg-white overflow-hidden shadow-sm rounded-lg border border-gray-200 hover:shadow-md transition-shadow duration-200">
        <div className="p-5">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className={`w-8 h-8 ${color} rounded-md flex items-center justify-center`}>
                {icon}
              </div>
            </div>
            <div className="ml-5 w-0 flex-1">
              <dl>
                <dt className="text-sm font-medium text-gray-500 truncate">{title}</dt>
                <dd className="text-lg font-medium text-gray-900">{value.toLocaleString()}</dd>
              </dl>
            </div>
          </div>
        </div>
      </div>
    </Link>
  );
});

interface QuickActionProps {
  title: string;
  description: string;
  href: string;
  icon: React.ReactNode;
  color: string;
}

function QuickAction({ title, description, href, icon, color }: QuickActionProps) {
  return (
    <Link href={href} className="block">
      <div className="bg-white overflow-hidden shadow-sm rounded-lg border border-gray-200 hover:shadow-md transition-shadow duration-200">
        <div className="p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className={`w-10 h-10 ${color} rounded-lg flex items-center justify-center`}>
                {icon}
              </div>
            </div>
            <div className="ml-4">
              <h3 className="text-lg font-medium text-gray-900">{title}</h3>
              <p className="text-sm text-gray-500">{description}</p>
            </div>
          </div>
        </div>
      </div>
    </Link>
  );
}

function DashboardContent() {
  // Use React Query for better caching and performance
  const { data: products, isLoading: productsLoading } = useProducts();
  const { data: brands, isLoading: brandsLoading } = useBrands();
  const { data: categories, isLoading: categoriesLoading } = useCategories();
  const { data: banners, isLoading: bannersLoading } = useBanners();

  // Memoize stats calculation to prevent unnecessary recalculations
  const stats = useMemo<DashboardStats>(() => ({
    totalProducts: products?.length || 0,
    totalBrands: brands?.length || 0,
    totalCategories: categories?.length || 0,
    totalBanners: banners?.length || 0,
  }), [products, brands, categories, banners]);

  const isLoading = productsLoading || brandsLoading || categoriesLoading || bannersLoading;

  // Memoize quick actions to prevent unnecessary re-renders
  const quickActions = useMemo(() => [
    {
      title: 'Add New Product',
      description: 'Create a new product with variants and photos',
      href: '/admins/products?action=create',
      icon: (
        <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
        </svg>
      ),
      color: 'bg-[#E6B120]',
    },
    {
      title: 'Manage Brands',
      description: 'Add, edit, or remove product brands',
      href: '/admins/brands',
      icon: (
        <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z" />
        </svg>
      ),
      color: 'bg-[#FFCD29]',
    },
    {
      title: 'Manage Categories',
      description: 'Organize products into categories',
      href: '/admins/categories',
      icon: (
        <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
        </svg>
      ),
      color: 'bg-gray-600',
    },
    {
      title: 'Update Banners',
      description: 'Manage website banners and promotions',
      href: '/admins/banners',
      icon: (
        <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
        </svg>
      ),
      color: 'bg-blue-600',
    },
  ], []);

  return (
    <div className="space-y-8">
      {/* Welcome Section */}
      <div className="bg-gradient-to-r from-[#E6B120] to-[#FFCD29] rounded-lg shadow-sm">
        <div className="px-6 py-8">
          <h1 className="text-3xl font-bold text-white">Welcome to GG Catalog Admin</h1>
          <p className="mt-2 text-lg text-white opacity-90">
            Manage your products, brands, categories, and more from this dashboard.
          </p>
        </div>
      </div>

      {/* Statistics Cards */}
      <div>
        <h2 className="text-lg font-medium text-gray-900 mb-4">Overview</h2>
        {isLoading ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="bg-white rounded-lg shadow-sm border border-gray-200 p-5">
                <div className="animate-pulse">
                  <div className="flex items-center">
                    <div className="w-8 h-8 bg-gray-200 rounded-md"></div>
                    <div className="ml-5 flex-1">
                      <div className="h-4 bg-gray-200 rounded w-20 mb-2"></div>
                      <div className="h-6 bg-gray-200 rounded w-12"></div>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <StatCard
              title="Total Products"
              value={stats.totalProducts}
              icon={
                <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
                </svg>
              }
              color="bg-[#E6B120]"
              href="/admins/products"
            />
            <StatCard
              title="Total Brands"
              value={stats.totalBrands}
              icon={
                <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z" />
                </svg>
              }
              color="bg-[#FFCD29]"
              href="/admins/brands"
            />
            <StatCard
              title="Total Categories"
              value={stats.totalCategories}
              icon={
                <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                </svg>
              }
              color="bg-gray-600"
              href="/admins/categories"
            />
            <StatCard
              title="Total Banners"
              value={stats.totalBanners}
              icon={
                <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                </svg>
              }
              color="bg-blue-600"
              href="/admins/banners"
            />
          </div>
        )}
      </div>

      {/* Quick Actions */}
      <div>
        <h2 className="text-lg font-medium text-gray-900 mb-4">Quick Actions</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {quickActions.map((action, index) => (
            <QuickAction key={index} {...action} />
          ))}
        </div>
      </div>

      {/* Recent Activity Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Recent Products */}
        <DashboardCard
          title="Recent Products"
          items={products || []}
          loading={productsLoading}
          emptyMessage="Get started by creating a new product."
          viewAllHref="/admins/products"
          createHref="/admins/products?action=create"
          renderItem={ProductItemRenderer}
          emptyIcon={
            <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
            </svg>
          }
        />

        {/* Recent Brands */}
        <LazyDashboardCard
          title="Recent Brands"
          items={brands || []}
          loading={brandsLoading}
          emptyMessage="Get started by creating a new brand."
          viewAllHref="/admins/brands"
          createHref="/admins/brands?action=create"
          renderItem={BrandItemRenderer}
          emptyIcon={
            <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z" />
            </svg>
          }
        />

        {/* Recent Categories */}
        <LazyDashboardCard
          title="Recent Categories"
          items={categories || []}
          loading={categoriesLoading}
          emptyMessage="Get started by creating a new category."
          viewAllHref="/admins/categories"
          createHref="/admins/categories?action=create"
          renderItem={CategoryItemRenderer}
          emptyIcon={
            <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
            </svg>
          }
        />

        {/* Recent Banners */}
        <LazyDashboardCard
          title="Recent Banners"
          items={banners || []}
          loading={bannersLoading}
          emptyMessage="Get started by creating a new banner."
          viewAllHref="/admins/banners"
          createHref="/admins/banners?action=create"
          renderItem={BannerItemRenderer}
          emptyIcon={
            <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
            </svg>
          }
        />
      </div>
    </div>
  );
}

export default function DashboardPage() {
  return (
    <ProtectedRoute>
      <AdminLayout>
        <DashboardContent />
      </AdminLayout>
    </ProtectedRoute>
  );
}
