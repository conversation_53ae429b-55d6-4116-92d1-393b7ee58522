/**
 * Image Upload Utilities
 * Provides unified functions for handling different types of image uploads
 * with proper validation, error handling, and type-specific processing
 */

export interface ImageUploadOptions {
  maxSizeBytes?: number;
  allowedTypes?: string[];
  quality?: number;
}

export interface ImageUploadResult {
  success: boolean;
  data?: {
    filename: string;
    url: string;
    size: number;
    dimensions: { width: number; height: number };
  };
  error?: string;
}

export interface CropOptions {
  aspectRatio: number;
  minWidth?: number;
  minHeight?: number;
}

// Default options for different image types
export const IMAGE_TYPE_OPTIONS = {
  product: {
    maxSizeBytes: 10 * 1024 * 1024, // 10MB
    allowedTypes: ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'],
    quality: 85,
    cropOptions: { aspectRatio: 1, minWidth: 200, minHeight: 200 }
  },
  brand: {
    maxSizeBytes: 5 * 1024 * 1024, // 5MB
    allowedTypes: ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'],
    quality: 85,
    cropOptions: { aspectRatio: 1, minWidth: 100, minHeight: 100 }
  },
  category: {
    maxSizeBytes: 5 * 1024 * 1024, // 5MB
    allowedTypes: ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'],
    quality: 85,
    cropOptions: { aspectRatio: 1, minWidth: 100, minHeight: 100 }
  },
  banner: {
    maxSizeBytes: 10 * 1024 * 1024, // 10MB
    allowedTypes: ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'],
    quality: 90,
    cropOptions: { aspectRatio: 13/5, minWidth: 260, minHeight: 100 }
  }
} as const;

export type ImageType = keyof typeof IMAGE_TYPE_OPTIONS;

/**
 * Validates an image file based on type-specific requirements
 */
export function validateImageFile(
  file: File, 
  imageType: ImageType
): { isValid: boolean; error?: string } {
  const options = IMAGE_TYPE_OPTIONS[imageType];
  
  // Check file type
  if (!options.allowedTypes.includes(file.type)) {
    return {
      isValid: false,
      error: `Invalid file type. Allowed types: ${options.allowedTypes.join(', ')}`
    };
  }
  
  // Check file size
  if (file.size > options.maxSizeBytes) {
    const maxSizeMB = Math.round(options.maxSizeBytes / (1024 * 1024));
    return {
      isValid: false,
      error: `File too large. Maximum size: ${maxSizeMB}MB`
    };
  }
  
  return { isValid: true };
}

/**
 * Gets the appropriate crop options for an image type
 */
export function getCropOptions(imageType: ImageType): CropOptions {
  return IMAGE_TYPE_OPTIONS[imageType].cropOptions;
}

/**
 * Creates a canvas element and draws the cropped image
 */
export function drawCroppedImage(
  image: HTMLImageElement,
  crop: { x: number; y: number; width: number; height: number },
  targetWidth?: number,
  targetHeight?: number
): HTMLCanvasElement {
  const canvas = document.createElement('canvas');
  const ctx = canvas.getContext('2d');
  
  if (!ctx) {
    throw new Error('Failed to get canvas context');
  }
  
  // Calculate final dimensions
  const finalWidth = targetWidth || crop.width;
  const finalHeight = targetHeight || crop.height;
  
  canvas.width = finalWidth;
  canvas.height = finalHeight;
  
  // Draw the cropped image
  ctx.drawImage(
    image,
    crop.x,
    crop.y,
    crop.width,
    crop.height,
    0,
    0,
    finalWidth,
    finalHeight
  );
  
  return canvas;
}

/**
 * Converts a canvas to a Blob with specified quality
 */
export function canvasToBlob(
  canvas: HTMLCanvasElement,
  quality: number = 0.85,
  type: string = 'image/jpeg'
): Promise<Blob> {
  return new Promise((resolve, reject) => {
    canvas.toBlob(
      (blob) => {
        if (blob) {
          resolve(blob);
        } else {
          reject(new Error('Failed to create blob from canvas'));
        }
      },
      type,
      quality
    );
  });
}

/**
 * Uploads an image to the server
 */
export async function uploadImage(
  imageBlob: Blob,
  filename: string,
  imageType: ImageType
): Promise<ImageUploadResult> {
  try {
    const formData = new FormData();
    formData.append('image', imageBlob, filename);
    
    const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5000/api';
    const uploadEndpoints = {
      product: `${API_BASE_URL}/upload/product-image`,
      brand: `${API_BASE_URL}/upload/brand-image`,
      category: `${API_BASE_URL}/upload/category-image`,
      banner: `${API_BASE_URL}/upload/banner-image`
    };
    
    const response = await fetch(uploadEndpoints[imageType], {
      method: 'POST',
      body: formData,
      credentials: 'include',
    });
    
    const result = await response.json();
    
    if (result.success) {
      return {
        success: true,
        data: result.data
      };
    } else {
      return {
        success: false,
        error: result.error || 'Upload failed'
      };
    }
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Network error during upload'
    };
  }
}

/**
 * Complete image upload workflow: validate, crop, and upload
 */
export async function processAndUploadImage(
  file: File,
  imageType: ImageType,
  cropData: { x: number; y: number; width: number; height: number },
  imageElement: HTMLImageElement
): Promise<ImageUploadResult> {
  try {
    // Validate the file
    const validation = validateImageFile(file, imageType);
    if (!validation.isValid) {
      return {
        success: false,
        error: validation.error
      };
    }
    
    // Get type-specific options
    const options = IMAGE_TYPE_OPTIONS[imageType];
    
    // Create cropped canvas
    const canvas = drawCroppedImage(imageElement, cropData);
    
    // Convert to blob
    const blob = await canvasToBlob(canvas, options.quality / 100);
    
    // Upload the image
    return await uploadImage(blob, file.name, imageType);
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to process image'
    };
  }
}

/**
 * Creates a preview URL for a file
 */
export function createPreviewUrl(file: File): Promise<string> {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = (e) => {
      if (e.target?.result) {
        resolve(e.target.result as string);
      } else {
        reject(new Error('Failed to read file'));
      }
    };
    reader.onerror = () => reject(new Error('Failed to read file'));
    reader.readAsDataURL(file);
  });
}

/**
 * Utility function to format file size for display
 */
export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

/**
 * Gets human-readable description for image type requirements
 */
export function getImageTypeDescription(imageType: ImageType): string {
  const options = IMAGE_TYPE_OPTIONS[imageType];
  const maxSizeMB = Math.round(options.maxSizeBytes / (1024 * 1024));
  const { aspectRatio } = options.cropOptions;
  
  let aspectDescription = '';
  if (aspectRatio === 1) {
    aspectDescription = 'square (1:1)';
  } else if (aspectRatio === 13/5) {
    aspectDescription = 'banner (13:5)';
  } else {
    aspectDescription = `${aspectRatio}:1`;
  }
  
  return `${imageType.charAt(0).toUpperCase() + imageType.slice(1)} image - ${aspectDescription} aspect ratio, max ${maxSizeMB}MB`;
}
