const multer = require('multer');
const sharp = require('sharp');
const path = require('path');
const fs = require('fs').promises;

// Create uploads directory if it doesn't exist
const createUploadsDir = async () => {
  // Point to NextJS public/uploads directory
  const uploadsDir = path.join(__dirname, '../../public/uploads');
  try {
    await fs.access(uploadsDir);
  } catch (error) {
    await fs.mkdir(uploadsDir, { recursive: true });
  }
  return uploadsDir;
};

// Configure multer for memory storage
const storage = multer.memoryStorage();

const fileFilter = (req, file, cb) => {
  // Check if file is an image
  if (file.mimetype.startsWith('image/')) {
    cb(null, true);
  } else {
    cb(new Error('Only image files are allowed!'), false);
  }
};

const upload = multer({
  storage: storage,
  fileFilter: fileFilter,
  limits: {
    fileSize: 10 * 1024 * 1024, // 10MB limit
  },
});

// Process and save image with specific aspect ratio and dimensions
const processImageWithAspectRatio = async (buffer, filename, options = {}) => {
  try {
    const uploadsDir = await createUploadsDir();

    // Default options
    const {
      aspectRatio = 1, // width/height ratio (1 = square, 13/5 = banner, etc.)
      targetWidth = 800,
      targetHeight = null, // Will be calculated from aspectRatio if not provided
      quality = 85,
      format = 'webp'
    } = options;

    // Calculate target height if not provided
    const finalHeight = targetHeight || Math.round(targetWidth / aspectRatio);

    // Get image metadata
    const metadata = await sharp(buffer).metadata();

    // Calculate crop dimensions based on aspect ratio
    let cropWidth, cropHeight, left, top;

    const imageAspectRatio = metadata.width / metadata.height;

    if (imageAspectRatio > aspectRatio) {
      // Image is wider than target aspect ratio - crop width
      cropHeight = metadata.height;
      cropWidth = Math.round(cropHeight * aspectRatio);
      left = Math.floor((metadata.width - cropWidth) / 2);
      top = 0;
    } else {
      // Image is taller than target aspect ratio - crop height
      cropWidth = metadata.width;
      cropHeight = Math.round(cropWidth / aspectRatio);
      left = 0;
      top = Math.floor((metadata.height - cropHeight) / 2);
    }

    // Process image: crop to aspect ratio, resize, convert format
    let sharpInstance = sharp(buffer)
      .extract({ left, top, width: cropWidth, height: cropHeight })
      .resize(targetWidth, finalHeight, {
        kernel: sharp.kernel.lanczos3,
        fit: 'cover',
        position: 'center'
      });

    // Apply format conversion
    if (format === 'webp') {
      sharpInstance = sharpInstance.webp({ quality });
    } else if (format === 'jpeg') {
      sharpInstance = sharpInstance.jpeg({ quality });
    } else if (format === 'png') {
      sharpInstance = sharpInstance.png({ quality });
    }

    const processedBuffer = await sharpInstance.toBuffer();

    // Generate filename with correct extension
    const extension = format === 'jpeg' ? 'jpg' : format;
    const processedFilename = filename.includes('.')
      ? filename.replace(/\.[^/.]+$/, `.${extension}`)
      : `${filename}.${extension}`;
    const filepath = path.join(uploadsDir, processedFilename);

    // Save processed image
    await fs.writeFile(filepath, processedBuffer);

    return {
      filename: processedFilename,
      path: filepath,
      size: processedBuffer.length,
      dimensions: { width: targetWidth, height: finalHeight }
    };
  } catch (error) {
    throw new Error(`Image processing failed: ${error.message}`);
  }
};

// Process product image (1:1 aspect ratio, 800x800)
const processProductImage = async (buffer, filename) => {
  return processImageWithAspectRatio(buffer, filename, {
    aspectRatio: 1,
    targetWidth: 800,
    quality: 85,
    format: 'webp'
  });
};

// Process brand image (1:1 aspect ratio, 400x400)
const processBrandImage = async (buffer, filename) => {
  return processImageWithAspectRatio(buffer, filename, {
    aspectRatio: 1,
    targetWidth: 400,
    quality: 85,
    format: 'webp'
  });
};

// Process category image (1:1 aspect ratio, 400x400)
const processCategoryImage = async (buffer, filename) => {
  return processImageWithAspectRatio(buffer, filename, {
    aspectRatio: 1,
    targetWidth: 400,
    quality: 85,
    format: 'webp'
  });
};

// Process banner image (13:5 aspect ratio, 1300x500)
const processBannerImage = async (buffer, filename) => {
  return processImageWithAspectRatio(buffer, filename, {
    aspectRatio: 13/5,
    targetWidth: 1300,
    targetHeight: 500,
    quality: 90,
    format: 'webp'
  });
};

// Legacy function for backward compatibility
const processImage = async (buffer, filename) => {
  return processProductImage(buffer, filename);
};

// Middleware for single image upload with type-specific processing
const uploadSingle = (fieldName, imageType = 'product') => {
  return async (req, res, next) => {
    const uploadMiddleware = upload.single(fieldName);

    uploadMiddleware(req, res, async (err) => {
      if (err) {
        if (err instanceof multer.MulterError) {
          if (err.code === 'LIMIT_FILE_SIZE') {
            return res.status(400).json({
              success: false,
              error: 'File too large. Maximum size is 10MB.'
            });
          }
        }
        return res.status(400).json({
          success: false,
          error: err.message
        });
      }

      if (!req.file) {
        return res.status(400).json({
          success: false,
          error: 'No image file provided.'
        });
      }

      try {
        // Generate unique filename
        const timestamp = Date.now();
        const randomString = Math.random().toString(36).substring(2, 15);
        const filename = `${timestamp}_${randomString}`;

        // Choose processing function based on image type
        let result;
        switch (imageType) {
          case 'brand':
            result = await processBrandImage(req.file.buffer, filename);
            break;
          case 'category':
            result = await processCategoryImage(req.file.buffer, filename);
            break;
          case 'banner':
            result = await processBannerImage(req.file.buffer, filename);
            break;
          case 'product':
          default:
            result = await processProductImage(req.file.buffer, filename);
            break;
        }

        // Add processed image info to request
        req.processedImage = result;

        next();
      } catch (error) {
        return res.status(500).json({
          success: false,
          error: error.message
        });
      }
    });
  };
};

// Middleware for multiple image upload (typically for products)
const uploadMultiple = (fieldName, maxCount = 5, imageType = 'product') => {
  return async (req, res, next) => {
    const uploadMiddleware = upload.array(fieldName, maxCount);

    uploadMiddleware(req, res, async (err) => {
      if (err) {
        if (err instanceof multer.MulterError) {
          if (err.code === 'LIMIT_FILE_SIZE') {
            return res.status(400).json({
              success: false,
              error: 'File too large. Maximum size is 10MB.'
            });
          }
          if (err.code === 'LIMIT_UNEXPECTED_FILE') {
            return res.status(400).json({
              success: false,
              error: `Too many files. Maximum is ${maxCount}.`
            });
          }
        }
        return res.status(400).json({
          success: false,
          error: err.message
        });
      }

      if (!req.files || req.files.length === 0) {
        return res.status(400).json({
          success: false,
          error: 'No image files provided.'
        });
      }

      try {
        const processedImages = [];

        for (let i = 0; i < req.files.length; i++) {
          const file = req.files[i];

          // Generate unique filename
          const timestamp = Date.now();
          const randomString = Math.random().toString(36).substring(2, 15);
          const filename = `${timestamp}_${randomString}_${i}`;

          // Choose processing function based on image type
          let result;
          switch (imageType) {
            case 'brand':
              result = await processBrandImage(file.buffer, filename);
              break;
            case 'category':
              result = await processCategoryImage(file.buffer, filename);
              break;
            case 'banner':
              result = await processBannerImage(file.buffer, filename);
              break;
            case 'product':
            default:
              result = await processProductImage(file.buffer, filename);
              break;
          }

          processedImages.push(result);
        }

        // Add processed images info to request
        req.processedImages = processedImages;

        next();
      } catch (error) {
        return res.status(500).json({
          success: false,
          error: error.message
        });
      }
    });
  };
};

// Delete image file
const deleteImage = async (filename) => {
  try {
    const uploadsDir = await createUploadsDir();
    const filepath = path.join(uploadsDir, filename);
    await fs.unlink(filepath);
    return true;
  } catch (error) {
    console.error('Error deleting image:', error);
    return false;
  }
};

module.exports = {
  uploadSingle,
  uploadMultiple,
  processImage,
  processImageWithAspectRatio,
  processProductImage,
  processBrandImage,
  processCategoryImage,
  processBannerImage,
  deleteImage,
  createUploadsDir
};
