import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { productAPI, brandAPI, categoryAPI, bannerAPI, uploadAPI } from './api';

// Query keys for consistent caching
export const queryKeys = {
  products: ['products'] as const,
  product: (id: number) => ['products', id] as const,
  brands: ['brands'] as const,
  brand: (id: number) => ['brands', id] as const,
  categories: ['categories'] as const,
  category: (id: number) => ['categories', id] as const,
  banners: ['banners'] as const,
  banner: (id: number) => ['banners', id] as const,
};

// Product queries
export function useProducts() {
  return useQuery({
    queryKey: queryKeys.products,
    queryFn: async () => {
      const response = await productAPI.getAllProducts();
      if (!response.success) {
        throw new Error(response.error || 'Failed to fetch products');
      }
      return response.data;
    },
  });
}

export function useProduct(id: number) {
  return useQuery({
    queryKey: queryKeys.product(id),
    queryFn: async () => {
      const response = await productAPI.getProductById(id);
      if (!response.success) {
        throw new Error(response.error || 'Failed to fetch product');
      }
      return response.data;
    },
    enabled: !!id,
  });
}

// Brand queries
export function useBrands() {
  return useQuery({
    queryKey: queryKeys.brands,
    queryFn: async () => {
      const response = await brandAPI.getAllBrands();
      if (!response.success) {
        throw new Error(response.error || 'Failed to fetch brands');
      }
      return response.data;
    },
  });
}

export function useBrand(id: number) {
  return useQuery({
    queryKey: queryKeys.brand(id),
    queryFn: async () => {
      const response = await brandAPI.getBrandById(id);
      if (!response.success) {
        throw new Error(response.error || 'Failed to fetch brand');
      }
      return response.data;
    },
    enabled: !!id,
  });
}

// Category queries
export function useCategories() {
  return useQuery({
    queryKey: queryKeys.categories,
    queryFn: async () => {
      const response = await categoryAPI.getAllCategories();
      if (!response.success) {
        throw new Error(response.error || 'Failed to fetch categories');
      }
      return response.data;
    },
  });
}

export function useCategory(id: number) {
  return useQuery({
    queryKey: queryKeys.category(id),
    queryFn: async () => {
      const response = await categoryAPI.getCategoryById(id);
      if (!response.success) {
        throw new Error(response.error || 'Failed to fetch category');
      }
      return response.data;
    },
    enabled: !!id,
  });
}

// Banner queries
export function useBanners() {
  return useQuery({
    queryKey: queryKeys.banners,
    queryFn: async () => {
      const response = await bannerAPI.getAllBanners();
      if (!response.success) {
        throw new Error(response.error || 'Failed to fetch banners');
      }
      return response.data;
    },
  });
}

export function useBanner(id: number) {
  return useQuery({
    queryKey: queryKeys.banner(id),
    queryFn: async () => {
      const response = await bannerAPI.getBannerById(id);
      if (!response.success) {
        throw new Error(response.error || 'Failed to fetch banner');
      }
      return response.data;
    },
    enabled: !!id,
  });
}

// Mutations with optimistic updates
export function useCreateProduct() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async (productData: any) => {
      const response = await productAPI.createProduct(productData);
      if (!response.success) {
        throw new Error(response.error || 'Failed to create product');
      }
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: queryKeys.products });
    },
  });
}

export function useUpdateProduct() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async ({ id, data }: { id: number; data: any }) => {
      const response = await productAPI.updateProduct(id, data);
      if (!response.success) {
        throw new Error(response.error || 'Failed to update product');
      }
      return response.data;
    },
    onSuccess: (_, { id }) => {
      queryClient.invalidateQueries({ queryKey: queryKeys.products });
      queryClient.invalidateQueries({ queryKey: queryKeys.product(id) });
    },
  });
}

export function useDeleteProduct() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async (id: number) => {
      const response = await productAPI.deleteProduct(id);
      if (!response.success) {
        throw new Error(response.error || 'Failed to delete product');
      }
      return response.data;
    },
    onSuccess: (_, id) => {
      queryClient.invalidateQueries({ queryKey: queryKeys.products });
      queryClient.removeQueries({ queryKey: queryKeys.product(id) });
    },
  });
}

// Brand mutations
export function useCreateBrand() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async (brandData: any) => {
      const response = await brandAPI.createBrand(brandData);
      if (!response.success) {
        throw new Error(response.error || 'Failed to create brand');
      }
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: queryKeys.brands });
    },
  });
}

export function useUpdateBrand() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async ({ id, data }: { id: number; data: any }) => {
      const response = await brandAPI.updateBrand(id, data);
      if (!response.success) {
        throw new Error(response.error || 'Failed to update brand');
      }
      return response.data;
    },
    onSuccess: (_, { id }) => {
      queryClient.invalidateQueries({ queryKey: queryKeys.brands });
      queryClient.invalidateQueries({ queryKey: queryKeys.brand(id) });
    },
  });
}

export function useDeleteBrand() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async (id: number) => {
      const response = await brandAPI.deleteBrand(id);
      if (!response.success) {
        throw new Error(response.error || 'Failed to delete brand');
      }
      return response.data;
    },
    onSuccess: (_, id) => {
      queryClient.invalidateQueries({ queryKey: queryKeys.brands });
      queryClient.removeQueries({ queryKey: queryKeys.brand(id) });
    },
  });
}

// Category mutations
export function useCreateCategory() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async (categoryData: any) => {
      const response = await categoryAPI.createCategory(categoryData);
      if (!response.success) {
        throw new Error(response.error || 'Failed to create category');
      }
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: queryKeys.categories });
    },
  });
}

export function useUpdateCategory() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async ({ id, data }: { id: number; data: any }) => {
      const response = await categoryAPI.updateCategory(id, data);
      if (!response.success) {
        throw new Error(response.error || 'Failed to update category');
      }
      return response.data;
    },
    onSuccess: (_, { id }) => {
      queryClient.invalidateQueries({ queryKey: queryKeys.categories });
      queryClient.invalidateQueries({ queryKey: queryKeys.category(id) });
    },
  });
}

export function useDeleteCategory() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async (id: number) => {
      const response = await categoryAPI.deleteCategory(id);
      if (!response.success) {
        throw new Error(response.error || 'Failed to delete category');
      }
      return response.data;
    },
    onSuccess: (_, id) => {
      queryClient.invalidateQueries({ queryKey: queryKeys.categories });
      queryClient.removeQueries({ queryKey: queryKeys.category(id) });
    },
  });
}

// Banner mutations
export function useCreateBanner() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async (bannerData: any) => {
      const response = await bannerAPI.createBanner(bannerData);
      if (!response.success) {
        throw new Error(response.error || 'Failed to create banner');
      }
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: queryKeys.banners });
    },
  });
}

export function useUpdateBanner() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async ({ id, data }: { id: number; data: any }) => {
      const response = await bannerAPI.updateBanner(id, data);
      if (!response.success) {
        throw new Error(response.error || 'Failed to update banner');
      }
      return response.data;
    },
    onSuccess: (_, { id }) => {
      queryClient.invalidateQueries({ queryKey: queryKeys.banners });
      queryClient.invalidateQueries({ queryKey: queryKeys.banner(id) });
    },
  });
}

export function useDeleteBanner() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async (id: number) => {
      const response = await bannerAPI.deleteBanner(id);
      if (!response.success) {
        throw new Error(response.error || 'Failed to delete banner');
      }
      return response.data;
    },
    onSuccess: (_, id) => {
      queryClient.invalidateQueries({ queryKey: queryKeys.banners });
      queryClient.removeQueries({ queryKey: queryKeys.banner(id) });
    },
  });
}
